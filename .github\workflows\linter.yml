name: Linter

concurrency:
  group: linter-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - 'main'
  pull_request:

jobs:
  golangci-lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          check-latest: true
          go-version-file: 'go.mod'

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v6
        with:
          version: latest
