name: Publish Docker Image

concurrency:
  group: docker-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - 'main'
    tags:
      - '*'

jobs:

  build:
    name: Build
    runs-on: ubuntu-latest
    steps:

      - name: Check out code into the Go module directory
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: all

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: latest

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Get Version
        id: shell
        run: |
          echo "version=$(git describe --abbrev=0 --tags HEAD | cut -d'v' -f 2)" >> $GITHUB_OUTPUT

      - name: Build and Push (dev)
        if: github.ref == 'refs/heads/main'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            ghcr.io/metatube-community/metatube-server:dev
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and Push (latest)
        if: startsWith(github.ref, 'refs/tags/')
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            ghcr.io/metatube-community/metatube-server:latest
            ghcr.io/metatube-community/metatube-server:${{ steps.shell.outputs.version }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
