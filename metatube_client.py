#!/usr/bin/env python3
"""
MetaTube SDK Go 客户端调用工具
支持搜索影片信息、获取详细信息等功能

使用示例:
    python metatube_client.py search "abp-178"
    python metatube_client.py search "abp-178" --provider fanza
    python metatube_client.py info fanza abp178
    python metatube_client.py server --port 8080
"""

import argparse
import json
import sys
import time
import subprocess
import os
import signal
import requests
from typing import Optional, Dict, List, Any
from urllib.parse import urljoin, quote


class MetaTubeClient:
    """MetaTube SDK Go 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8080", token: str = None):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.session = requests.Session()
        
        # 设置认证头
        if self.token:
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None, **kwargs) -> Dict:
        """发起HTTP请求"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        try:
            response = self.session.request(method, url, params=params, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    print(f"错误详情: {error_data}")
                except:
                    print(f"响应内容: {e.response.text}")
            sys.exit(1)
    
    def search_movie(self, keyword: str, provider: str = None, fallback: bool = True) -> List[Dict]:
        """搜索影片"""
        params = {
            'q': keyword,
            'fallback': fallback
        }
        if provider:
            params['provider'] = provider
        
        print(f"搜索影片: {keyword}")
        if provider:
            print(f"指定Provider: {provider}")
        
        result = self._make_request('GET', '/v1/movies/search', params=params)
        return result.get('data', [])
    
    def get_movie_info(self, provider: str, movie_id: str, lazy: bool = True) -> Dict:
        """获取影片详细信息"""
        params = {'lazy': lazy}
        endpoint = f'/v1/movies/{provider}/{movie_id}'
        
        print(f"获取影片信息: {provider}/{movie_id}")
        
        result = self._make_request('GET', endpoint, params=params)
        return result.get('data', {})
    
    def search_actor(self, keyword: str, provider: str = None, fallback: bool = True) -> List[Dict]:
        """搜索演员"""
        params = {
            'q': keyword,
            'fallback': fallback
        }
        if provider:
            params['provider'] = provider
        
        print(f"搜索演员: {keyword}")
        if provider:
            print(f"指定Provider: {provider}")
        
        result = self._make_request('GET', '/v1/actors/search', params=params)
        return result.get('data', [])
    
    def get_actor_info(self, provider: str, actor_id: str, lazy: bool = True) -> Dict:
        """获取演员详细信息"""
        params = {'lazy': lazy}
        endpoint = f'/v1/actors/{provider}/{actor_id}'
        
        print(f"获取演员信息: {provider}/{actor_id}")
        
        result = self._make_request('GET', endpoint, params=params)
        return result.get('data', {})
    
    def get_providers(self) -> Dict:
        """获取可用的Providers列表"""
        result = self._make_request('GET', '/v1/providers')
        return result.get('data', {})
    
    def check_health(self) -> bool:
        """检查服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False


class MetaTubeServer:
    """MetaTube 服务器管理"""
    
    def __init__(self):
        self.process = None
        self.server_path = None
        self._find_server_binary()
    
    def _find_server_binary(self):
        """查找服务器二进制文件"""
        possible_paths = [
            './metatube-server',
            './build/metatube-server',
            './cmd/server/metatube-server',
            'metatube-server'
        ]
        
        for path in possible_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                self.server_path = path
                return
        
        # 尝试构建
        if os.path.isfile('go.mod'):
            print("未找到服务器二进制文件，尝试构建...")
            try:
                subprocess.run(['go', 'build', '-o', 'metatube-server', './cmd/server'], 
                             check=True, cwd='.')
                self.server_path = './metatube-server'
                print("构建成功!")
            except subprocess.CalledProcessError:
                print("构建失败，请手动构建服务器")
                sys.exit(1)
        else:
            print("未找到服务器二进制文件，请先构建项目")
            sys.exit(1)
    
    def start(self, port: int = 8080, token: str = None, dsn: str = None, **kwargs):
        """启动服务器"""
        if self.process and self.process.poll() is None:
            print("服务器已在运行中")
            return
        
        cmd = [self.server_path, '-port', str(port)]
        
        if token:
            cmd.extend(['-token', token])
        if dsn:
            cmd.extend(['-dsn', dsn])
        
        # 添加其他参数
        for key, value in kwargs.items():
            if value is not None:
                cmd.extend([f'-{key.replace("_", "-")}', str(value)])
        
        print(f"启动服务器: {' '.join(cmd)}")
        
        try:
            self.process = subprocess.Popen(cmd)
            
            # 等待服务器启动
            client = MetaTubeClient(f"http://localhost:{port}", token)
            for i in range(30):  # 等待30秒
                if client.check_health():
                    print(f"服务器已启动，监听端口: {port}")
                    return
                time.sleep(1)
            
            print("服务器启动超时")
            self.stop()
            sys.exit(1)
            
        except Exception as e:
            print(f"启动服务器失败: {e}")
            sys.exit(1)
    
    def stop(self):
        """停止服务器"""
        if self.process and self.process.poll() is None:
            print("正在停止服务器...")
            self.process.terminate()
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.process.wait()
            print("服务器已停止")
        self.process = None
    
    def is_running(self) -> bool:
        """检查服务器是否运行中"""
        return self.process and self.process.poll() is None


def format_movie_result(movie: Dict) -> str:
    """格式化影片搜索结果"""
    lines = []
    lines.append(f"📺 {movie.get('title', 'N/A')}")
    lines.append(f"   ID: {movie.get('id', 'N/A')}")
    lines.append(f"   番号: {movie.get('number', 'N/A')}")
    lines.append(f"   Provider: {movie.get('provider', 'N/A')}")
    lines.append(f"   评分: {movie.get('score', 'N/A')}")
    lines.append(f"   发行日期: {movie.get('release_date', 'N/A')}")
    
    if movie.get('actors'):
        actors = ', '.join(movie['actors'][:3])  # 只显示前3个演员
        if len(movie['actors']) > 3:
            actors += f" 等{len(movie['actors'])}人"
        lines.append(f"   演员: {actors}")
    
    lines.append(f"   链接: {movie.get('homepage', 'N/A')}")
    lines.append("")
    
    return '\n'.join(lines)


def format_movie_info(info: Dict) -> str:
    """格式化影片详细信息"""
    lines = []
    lines.append(f"🎬 {info.get('title', 'N/A')}")
    lines.append(f"   ID: {info.get('id', 'N/A')}")
    lines.append(f"   番号: {info.get('number', 'N/A')}")
    lines.append(f"   Provider: {info.get('provider', 'N/A')}")
    lines.append(f"   评分: {info.get('score', 'N/A')}")
    lines.append(f"   时长: {info.get('runtime', 'N/A')} 分钟")
    lines.append(f"   发行日期: {info.get('release_date', 'N/A')}")
    lines.append(f"   制作商: {info.get('maker', 'N/A')}")
    lines.append(f"   发行商: {info.get('label', 'N/A')}")
    lines.append(f"   系列: {info.get('series', 'N/A')}")
    lines.append(f"   导演: {info.get('director', 'N/A')}")
    
    if info.get('actors'):
        lines.append(f"   演员: {', '.join(info['actors'])}")
    
    if info.get('genres'):
        lines.append(f"   类型: {', '.join(info['genres'])}")
    
    if info.get('summary'):
        lines.append(f"   简介: {info['summary'][:100]}...")
    
    lines.append(f"   封面: {info.get('cover_url', 'N/A')}")
    lines.append(f"   链接: {info.get('homepage', 'N/A')}")
    
    return '\n'.join(lines)


def main():
    parser = argparse.ArgumentParser(description='MetaTube SDK Go 客户端工具')
    parser.add_argument('--url', default='http://localhost:8080', help='服务器地址')
    parser.add_argument('--token', help='访问令牌')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 搜索命令
    search_parser = subparsers.add_parser('search', help='搜索影片')
    search_parser.add_argument('keyword', help='搜索关键词')
    search_parser.add_argument('--provider', help='指定Provider')
    search_parser.add_argument('--no-fallback', action='store_true', help='禁用回退搜索')
    
    # 获取信息命令
    info_parser = subparsers.add_parser('info', help='获取影片详细信息')
    info_parser.add_argument('provider', help='Provider名称')
    info_parser.add_argument('movie_id', help='影片ID')
    info_parser.add_argument('--no-lazy', action='store_true', help='禁用懒加载')
    
    # 搜索演员命令
    actor_search_parser = subparsers.add_parser('actor-search', help='搜索演员')
    actor_search_parser.add_argument('keyword', help='搜索关键词')
    actor_search_parser.add_argument('--provider', help='指定Provider')
    
    # 获取演员信息命令
    actor_info_parser = subparsers.add_parser('actor-info', help='获取演员详细信息')
    actor_info_parser.add_argument('provider', help='Provider名称')
    actor_info_parser.add_argument('actor_id', help='演员ID')
    
    # 列出Providers命令
    subparsers.add_parser('providers', help='列出可用的Providers')
    
    # 服务器命令
    server_parser = subparsers.add_parser('server', help='启动本地服务器')
    server_parser.add_argument('--port', type=int, default=8080, help='端口号')
    server_parser.add_argument('--server-token', help='服务器访问令牌')
    server_parser.add_argument('--dsn', help='数据库连接字符串')
    server_parser.add_argument('--request-timeout', help='请求超时时间')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 服务器命令特殊处理
    if args.command == 'server':
        server = MetaTubeServer()
        
        def signal_handler(sig, frame):
            print("\n收到中断信号，正在停止服务器...")
            server.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            server.start(
                port=args.port,
                token=args.server_token,
                dsn=args.dsn,
                request_timeout=args.request_timeout
            )
            
            print("按 Ctrl+C 停止服务器")
            while server.is_running():
                time.sleep(1)
                
        except KeyboardInterrupt:
            server.stop()
        
        return
    
    # 客户端命令
    client = MetaTubeClient(args.url, args.token)
    
    try:
        if args.command == 'search':
            results = client.search_movie(
                args.keyword, 
                args.provider, 
                not args.no_fallback
            )
            
            if results:
                print(f"找到 {len(results)} 个结果:\n")
                for movie in results:
                    print(format_movie_result(movie))
            else:
                print("未找到相关影片")
        
        elif args.command == 'info':
            info = client.get_movie_info(
                args.provider, 
                args.movie_id, 
                not args.no_lazy
            )
            
            if info:
                print(format_movie_info(info))
            else:
                print("未找到影片信息")
        
        elif args.command == 'actor-search':
            results = client.search_actor(args.keyword, args.provider)
            
            if results:
                print(f"找到 {len(results)} 个演员:\n")
                for actor in results:
                    print(f"👤 {actor.get('name', 'N/A')}")
                    print(f"   ID: {actor.get('id', 'N/A')}")
                    print(f"   Provider: {actor.get('provider', 'N/A')}")
                    print(f"   链接: {actor.get('homepage', 'N/A')}")
                    print()
            else:
                print("未找到相关演员")
        
        elif args.command == 'actor-info':
            info = client.get_actor_info(args.provider, args.actor_id)
            
            if info:
                print(f"👤 {info.get('name', 'N/A')}")
                print(f"   ID: {info.get('id', 'N/A')}")
                print(f"   Provider: {info.get('provider', 'N/A')}")
                print(f"   生日: {info.get('birthday', 'N/A')}")
                print(f"   身高: {info.get('height', 'N/A')}")
                print(f"   三围: {info.get('measurements', 'N/A')}")
                print(f"   头像: {info.get('avatar_url', 'N/A')}")
                print(f"   链接: {info.get('homepage', 'N/A')}")
            else:
                print("未找到演员信息")
        
        elif args.command == 'providers':
            providers = client.get_providers()
            
            print("可用的 Movie Providers:")
            for name, info in providers.get('movie', {}).items():
                print(f"  {name}: {info.get('url', 'N/A')} (优先级: {info.get('priority', 'N/A')})")
            
            print("\n可用的 Actor Providers:")
            for name, info in providers.get('actor', {}).items():
                print(f"  {name}: {info.get('url', 'N/A')} (优先级: {info.get('priority', 'N/A')})")
    
    except Exception as e:
        print(f"执行命令失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
