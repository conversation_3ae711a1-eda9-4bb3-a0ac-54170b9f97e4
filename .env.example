# MetaTube 云部署环境变量配置
# 复制此文件为 .env 并修改相应的值

# ===========================================
# 基本配置
# ===========================================

# 访问令牌（强烈建议设置，用于API认证）
TOKEN=your-super-secret-token-here

# 请求超时时间
REQUEST_TIMEOUT=60s

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL 数据库配置
POSTGRES_USER=metatube
POSTGRES_PASSWORD=your-strong-database-password-here
POSTGRES_DB=metatube

# 数据库连接池配置
DB_MAX_IDLE_CONNS=10
DB_MAX_OPEN_CONNS=100

# ===========================================
# 代理配置（如果需要访问被限制的网站）
# ===========================================

# HTTP/HTTPS 代理
# 示例: http://127.0.0.1:7890 或 socks5://127.0.0.1:1080
HTTP_PROXY=
HTTPS_PROXY=

# ===========================================
# Provider 优先级配置
# ===========================================

# FANZA (官方源，最高优先级)
FANZA_PRIORITY=2000
FANZA_TIMEOUT=30s

# JavBus (备用源)
JAVBUS_PRIORITY=1500
JAVBUS_TIMEOUT=30s

# JAVDB (备用源)
JAVDB_PRIORITY=1400

# DAHLIA (备用源)
DAHLIA_PRIORITY=995

# 禁用某些 Provider (设置为 0)
# JAVFREE_PRIORITY=0
# AIRAV_PRIORITY=0

# ===========================================
# 监控配置（可选）
# ===========================================

# Grafana 管理员密码
GRAFANA_PASSWORD=admin123

# ===========================================
# SSL 证书配置（如果使用 HTTPS）
# ===========================================

# 域名
DOMAIN=your-domain.com

# SSL 证书路径
SSL_CERT_PATH=./ssl/fullchain.pem
SSL_KEY_PATH=./ssl/privkey.pem

# ===========================================
# 云服务商特定配置
# ===========================================

# 阿里云
# ALICLOUD_ACCESS_KEY=
# ALICLOUD_SECRET_KEY=

# 腾讯云
# TENCENTCLOUD_SECRET_ID=
# TENCENTCLOUD_SECRET_KEY=

# AWS
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=us-west-2

# ===========================================
# 高级配置
# ===========================================

# 时区设置
TZ=Asia/Shanghai

# 日志级别
LOG_LEVEL=info

# 并发限制
MAX_CONCURRENT_REQUESTS=50

# 缓存配置
CACHE_TTL=3600

# ===========================================
# 安全配置
# ===========================================

# 允许的来源（CORS）
ALLOWED_ORIGINS=*

# 速率限制
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60s

# ===========================================
# 备份配置
# ===========================================

# 自动备份间隔（小时）
BACKUP_INTERVAL=24

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份存储路径
BACKUP_PATH=./backups
