#!/usr/bin/env python3
"""
MetaTube 云平台快速部署配置生成器
支持 Railway、Render、Fly.io、Heroku 等主流云平台
"""

import json
import yaml
import os
import secrets
import argparse
from typing import Dict, Any


class CloudDeployGenerator:
    """云平台部署配置生成器"""
    
    def __init__(self):
        self.image_name = "ghcr.io/metatube-community/metatube-server:latest"
        self.port = 8080
        self.token = secrets.token_hex(32)
    
    def generate_railway_config(self) -> Dict[str, Any]:
        """生成 Railway 部署配置"""
        return {
            "deployment_info": {
                "platform": "Railway",
                "image": self.image_name,
                "port": self.port,
                "command": "-port 8080 -db-auto-migrate",
                "environment_variables": {
                    "TOKEN": self.token,
                    "GIN_MODE": "release",
                    "DSN": "sqlite:///data/metatube.db"
                },
                "network": {
                    "container_port": self.port,
                    "public_access": True
                }
            },
            "instructions": [
                "1. 登录 Railway (https://railway.app)",
                "2. 点击 'New Project' -> 'Deploy from Docker Image'",
                f"3. 输入镜像地址: {self.image_name}",
                "4. 在 'Variables' 标签页添加环境变量",
                "5. 在 'Settings' 标签页配置启动命令",
                "6. 点击 'Deploy' 开始部署"
            ]
        }
    
    def generate_render_config(self) -> Dict[str, Any]:
        """生成 Render 部署配置"""
        return {
            "deployment_info": {
                "platform": "Render",
                "service_type": "Web Service",
                "image_url": self.image_name,
                "port": self.port,
                "start_command": f"-port {self.port} -db-auto-migrate -dsn sqlite:///data/metatube.db",
                "environment_variables": {
                    "TOKEN": self.token,
                    "GIN_MODE": "release",
                    "PORT": str(self.port)
                }
            },
            "instructions": [
                "1. 登录 Render (https://render.com)",
                "2. 点击 'New' -> 'Web Service'",
                "3. 选择 'Deploy an existing image'",
                f"4. 输入镜像 URL: {self.image_name}",
                "5. 配置服务名称和地区",
                "6. 添加环境变量",
                "7. 点击 'Create Web Service'"
            ]
        }
    
    def generate_flyio_config(self) -> Dict[str, Any]:
        """生成 Fly.io 部署配置"""
        fly_toml = {
            "app": "metatube-app",
            "primary_region": "nrt",
            "build": {
                "image": self.image_name
            },
            "http_service": {
                "internal_port": self.port,
                "force_https": True,
                "checks": [{
                    "grace_period": "10s",
                    "interval": "30s",
                    "method": "GET",
                    "path": "/",
                    "timeout": "5s"
                }]
            },
            "env": {
                "GIN_MODE": "release",
                "PORT": str(self.port)
            },
            "mounts": [{
                "source": "metatube_data",
                "destination": "/data"
            }]
        }
        
        return {
            "deployment_info": {
                "platform": "Fly.io",
                "config_file": "fly.toml",
                "config_content": fly_toml
            },
            "instructions": [
                "1. 安装 Fly CLI: https://fly.io/docs/getting-started/installing-flyctl/",
                "2. 登录: fly auth login",
                "3. 创建应用: fly apps create metatube-app",
                "4. 保存 fly.toml 配置文件",
                f"5. 设置密钥: fly secrets set TOKEN={self.token}",
                "6. 创建存储卷: fly volumes create metatube_data --region nrt --size 1",
                "7. 部署: fly deploy"
            ]
        }
    
    def generate_heroku_config(self) -> Dict[str, Any]:
        """生成 Heroku 部署配置"""
        heroku_yml = {
            "build": {
                "docker": {
                    "web": "Dockerfile.cloud"
                }
            },
            "run": {
                "web": f"/app/metatube-server -port $PORT -db-auto-migrate -token {self.token}"
            }
        }
        
        return {
            "deployment_info": {
                "platform": "Heroku",
                "config_file": "heroku.yml",
                "config_content": heroku_yml,
                "environment_variables": {
                    "TOKEN": self.token,
                    "GIN_MODE": "release"
                }
            },
            "instructions": [
                "1. 安装 Heroku CLI: https://devcenter.heroku.com/articles/heroku-cli",
                "2. 登录: heroku login",
                "3. 创建应用: heroku create your-app-name",
                "4. 设置构建方式: heroku stack:set container",
                "5. 保存 heroku.yml 配置文件",
                "6. 设置环境变量: heroku config:set TOKEN=" + self.token,
                "7. 部署: git push heroku main"
            ]
        }
    
    def generate_digitalocean_config(self) -> Dict[str, Any]:
        """生成 DigitalOcean App Platform 配置"""
        app_spec = {
            "name": "metatube-app",
            "services": [{
                "name": "web",
                "image": {
                    "registry_type": "DOCKER_HUB",
                    "registry": "ghcr.io",
                    "repository": "metatube-community/metatube-server",
                    "tag": "latest"
                },
                "instance_count": 1,
                "instance_size_slug": "basic-xxs",
                "http_port": self.port,
                "run_command": f"-port {self.port} -db-auto-migrate",
                "environment_slug": "docker",
                "envs": [
                    {"key": "TOKEN", "value": self.token},
                    {"key": "GIN_MODE", "value": "release"}
                ]
            }]
        }
        
        return {
            "deployment_info": {
                "platform": "DigitalOcean App Platform",
                "config_file": "app.yaml",
                "config_content": app_spec
            },
            "instructions": [
                "1. 登录 DigitalOcean (https://cloud.digitalocean.com)",
                "2. 进入 App Platform",
                "3. 点击 'Create App'",
                "4. 选择 'Docker Hub' 或上传 app.yaml",
                "5. 配置应用设置",
                "6. 点击 'Launch App'"
            ]
        }
    
    def save_config_file(self, config: Dict[str, Any], filename: str):
        """保存配置文件"""
        platform = config["deployment_info"]["platform"]
        
        if "config_file" in config["deployment_info"]:
            config_filename = config["deployment_info"]["config_file"]
            config_content = config["deployment_info"]["config_content"]
            
            if config_filename.endswith('.yml') or config_filename.endswith('.yaml'):
                with open(config_filename, 'w') as f:
                    yaml.dump(config_content, f, default_flow_style=False)
                print(f"✅ 已生成 {platform} 配置文件: {config_filename}")
            elif config_filename.endswith('.toml'):
                # 简单的 TOML 生成（仅支持基本结构）
                with open(config_filename, 'w') as f:
                    self._write_toml(config_content, f)
                print(f"✅ 已生成 {platform} 配置文件: {config_filename}")
        
        # 保存完整配置到 JSON 文件
        with open(filename, 'w') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 已生成 {platform} 部署配置: {filename}")
    
    def _write_toml(self, data: Dict[str, Any], file, indent=0):
        """简单的 TOML 写入器"""
        for key, value in data.items():
            if isinstance(value, dict):
                if indent == 0:
                    file.write(f"[{key}]\n")
                else:
                    file.write(f"[{key}]\n")
                self._write_toml(value, file, indent + 1)
                file.write("\n")
            elif isinstance(value, list):
                if all(isinstance(item, dict) for item in value):
                    for item in value:
                        file.write(f"[[{key}]]\n")
                        self._write_toml(item, file, indent + 1)
                        file.write("\n")
                else:
                    file.write(f"{key} = {json.dumps(value)}\n")
            else:
                if isinstance(value, str):
                    file.write(f'{key} = "{value}"\n')
                else:
                    file.write(f"{key} = {json.dumps(value)}\n")
    
    def print_instructions(self, config: Dict[str, Any]):
        """打印部署说明"""
        platform = config["deployment_info"]["platform"]
        print(f"\n🚀 {platform} 部署说明:")
        print("=" * 50)
        
        for i, instruction in enumerate(config["instructions"], 1):
            print(f"{i}. {instruction}")
        
        print(f"\n📋 配置信息:")
        deployment_info = config["deployment_info"]
        
        if "image" in deployment_info:
            print(f"镜像地址: {deployment_info['image']}")
        if "image_url" in deployment_info:
            print(f"镜像地址: {deployment_info['image_url']}")
        
        print(f"端口: {deployment_info.get('port', self.port)}")
        
        if "command" in deployment_info:
            print(f"启动命令: {deployment_info['command']}")
        if "start_command" in deployment_info:
            print(f"启动命令: {deployment_info['start_command']}")
        
        if "environment_variables" in deployment_info:
            print(f"\n环境变量:")
            for key, value in deployment_info["environment_variables"].items():
                if key == "TOKEN":
                    print(f"  {key}: {value[:8]}... (已截断)")
                else:
                    print(f"  {key}: {value}")


def main():
    parser = argparse.ArgumentParser(description="MetaTube 云平台部署配置生成器")
    parser.add_argument("platform", nargs="?", 
                       choices=["railway", "render", "flyio", "heroku", "digitalocean", "all"],
                       default="all",
                       help="目标云平台")
    parser.add_argument("--image", default="ghcr.io/metatube-community/metatube-server:latest",
                       help="Docker 镜像地址")
    parser.add_argument("--port", type=int, default=8080,
                       help="服务端口")
    parser.add_argument("--token", help="API 认证令牌（不指定则自动生成）")
    
    args = parser.parse_args()
    
    generator = CloudDeployGenerator()
    generator.image_name = args.image
    generator.port = args.port
    
    if args.token:
        generator.token = args.token
    
    platforms = {
        "railway": generator.generate_railway_config,
        "render": generator.generate_render_config,
        "flyio": generator.generate_flyio_config,
        "heroku": generator.generate_heroku_config,
        "digitalocean": generator.generate_digitalocean_config
    }
    
    if args.platform == "all":
        print("🎯 生成所有云平台部署配置")
        print("=" * 50)
        
        for platform_name, generator_func in platforms.items():
            config = generator_func()
            filename = f"{platform_name}-deploy.json"
            generator.save_config_file(config, filename)
            generator.print_instructions(config)
            print()
    else:
        if args.platform in platforms:
            config = platforms[args.platform]()
            filename = f"{args.platform}-deploy.json"
            generator.save_config_file(config, filename)
            generator.print_instructions(config)
        else:
            print(f"❌ 不支持的平台: {args.platform}")
            return
    
    print("\n🎉 配置生成完成！")
    print("\n💡 提示:")
    print("1. 请妥善保管生成的 TOKEN，这是 API 访问的密钥")
    print("2. 部署完成后，使用 test_deployment.py 验证部署")
    print("3. 如需自定义配置，请编辑生成的配置文件")


if __name__ == "__main__":
    main()
