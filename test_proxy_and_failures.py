#!/usr/bin/env python3
"""
测试代理设置和错误显示功能
"""

import time
import subprocess
import sys
from metatube_client import MetaTubeClient, MetaTubeServer


def test_proxy_functionality():
    """测试代理功能"""
    print("=== 测试代理功能 ===")
    
    server = MetaTubeServer()
    
    try:
        # 测试不使用代理
        print("1. 测试不使用代理...")
        server.start(port=8084)
        
        client = MetaTubeClient("http://localhost:8084")
        if client.check_health():
            print("✅ 无代理启动成功")
        
        server.stop()
        time.sleep(2)
        
        # 测试使用代理（这里使用一个无效的代理来测试设置是否生效）
        print("2. 测试代理设置...")
        server.start(port=8084, http_proxy="http://127.0.0.1:9999")  # 无效代理
        
        client = MetaTubeClient("http://localhost:8084")
        if client.check_health():
            print("✅ 代理设置已应用（服务器仍可启动）")
        
        server.stop()
        print("✅ 代理功能测试完成")
        
    except Exception as e:
        print(f"❌ 代理功能测试失败: {e}")
        server.stop()
        return False
    
    return True


def test_failure_display():
    """测试错误显示功能"""
    print("\n=== 测试错误显示功能 ===")
    
    server = MetaTubeServer()
    
    try:
        print("启动服务器...")
        server.start(port=8085)
        
        client = MetaTubeClient("http://localhost:8085")
        
        print("测试搜索并显示失败信息...")
        # 搜索一个可能不存在的内容来触发一些失败
        results = client.search_movie("nonexistent-movie-12345", show_failures=True)
        
        print(f"搜索结果数量: {len(results)}")
        print("✅ 错误显示功能测试完成")
        
    except Exception as e:
        print(f"❌ 错误显示功能测试失败: {e}")
        return False
    
    finally:
        server.stop()
    
    return True


def test_command_line_proxy():
    """测试命令行代理参数"""
    print("\n=== 测试命令行代理参数 ===")
    
    try:
        # 测试快速启动脚本的代理参数
        print("测试 quick_start.py 代理参数...")
        result = subprocess.run([
            sys.executable, "quick_start.py", "help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "proxy" in result.stdout.lower():
            print("✅ quick_start.py 支持代理参数")
        else:
            print("⚠️  quick_start.py 代理参数测试未通过")
        
        # 测试主客户端的代理参数
        print("测试 metatube_client.py 代理参数...")
        result = subprocess.run([
            sys.executable, "metatube_client.py", "server", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "proxy" in result.stdout.lower():
            print("✅ metatube_client.py 支持代理参数")
        else:
            print("⚠️  metatube_client.py 代理参数测试未通过")
        
        print("✅ 命令行代理参数测试完成")
        
    except Exception as e:
        print(f"❌ 命令行代理参数测试失败: {e}")
        return False
    
    return True


def demo_new_features():
    """演示新功能"""
    print("\n=== 新功能演示 ===")
    
    server = MetaTubeServer()
    
    try:
        print("1. 启动服务器（模拟代理设置）...")
        # 这里我们不使用真实的代理，只是演示参数传递
        server.start(port=8086, http_proxy=None, https_proxy=None)
        
        client = MetaTubeClient("http://localhost:8086")
        
        print("2. 演示错误显示功能...")
        print("   搜索 'abp-178' 并显示失败的 provider...")
        
        # 这会显示哪些 provider 搜索失败
        results = client.search_movie("abp-178", show_failures=True)
        
        print(f"   找到 {len(results)} 个结果")
        
        if results:
            print("   第一个结果:")
            first = results[0]
            print(f"   - 标题: {first.get('title', 'N/A')}")
            print(f"   - Provider: {first.get('provider', 'N/A')}")
            print(f"   - ID: {first.get('id', 'N/A')}")
        
        print("\n3. 演示 Provider 状态检查...")
        providers = client.get_providers()
        movie_providers = providers.get('movie', {})
        print(f"   可用的 Movie Providers: {len(movie_providers)} 个")
        
        # 显示前几个 provider
        for i, (name, info) in enumerate(list(movie_providers.items())[:3]):
            priority = info.get('priority', 'N/A')
            print(f"   - {name} (优先级: {priority})")
        
        print("\n✅ 新功能演示完成！")
        
    except Exception as e:
        print(f"❌ 新功能演示失败: {e}")
        print("💡 这可能是由于网络问题或 provider 不可用")
    
    finally:
        server.stop()


def main():
    print("🧪 MetaTube 新功能测试")
    print("=" * 50)
    
    tests = [
        ("代理功能", test_proxy_functionality),
        ("错误显示功能", test_failure_display),
        ("命令行代理参数", test_command_line_proxy),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n🔍 测试: {name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 测试通过")
            else:
                print(f"❌ {name} 测试失败")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    # 运行演示
    demo_new_features()
    
    print("\n🎯 使用建议:")
    print("1. 如果遇到地区限制，使用代理:")
    print("   python metatube_client.py server --http-proxy http://127.0.0.1:7890")
    print()
    print("2. 查看搜索失败原因:")
    print("   python metatube_client.py search 'abp-178' --show-failures")
    print()
    print("3. 快速搜索使用代理:")
    print("   python quick_start.py --http-proxy http://127.0.0.1:7890")


if __name__ == "__main__":
    main()
