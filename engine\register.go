package engine

// Register Providers

import (
	_ "github.com/metatube-community/metatube-sdk-go/provider/10musume"
	_ "github.com/metatube-community/metatube-sdk-go/provider/1pondo"
	_ "github.com/metatube-community/metatube-sdk-go/provider/av-league"
	_ "github.com/metatube-community/metatube-sdk-go/provider/avbase"
	_ "github.com/metatube-community/metatube-sdk-go/provider/aventertainments"
	_ "github.com/metatube-community/metatube-sdk-go/provider/c0930"
	_ "github.com/metatube-community/metatube-sdk-go/provider/caribbeancom"
	_ "github.com/metatube-community/metatube-sdk-go/provider/caribbeancompr"
	_ "github.com/metatube-community/metatube-sdk-go/provider/dahlia"
	_ "github.com/metatube-community/metatube-sdk-go/provider/duga"
	_ "github.com/metatube-community/metatube-sdk-go/provider/faleno"
	_ "github.com/metatube-community/metatube-sdk-go/provider/fanza"
	_ "github.com/metatube-community/metatube-sdk-go/provider/fc2"
	_ "github.com/metatube-community/metatube-sdk-go/provider/fc2hub"
	_ "github.com/metatube-community/metatube-sdk-go/provider/gcolle"
	_ "github.com/metatube-community/metatube-sdk-go/provider/getchu"
	_ "github.com/metatube-community/metatube-sdk-go/provider/gfriends"
	_ "github.com/metatube-community/metatube-sdk-go/provider/h0930"
	_ "github.com/metatube-community/metatube-sdk-go/provider/h4610"
	_ "github.com/metatube-community/metatube-sdk-go/provider/heydouga"
	_ "github.com/metatube-community/metatube-sdk-go/provider/heyzo"
	_ "github.com/metatube-community/metatube-sdk-go/provider/jav321"
	_ "github.com/metatube-community/metatube-sdk-go/provider/javbus"
	_ "github.com/metatube-community/metatube-sdk-go/provider/javfree"
	_ "github.com/metatube-community/metatube-sdk-go/provider/kin8tengoku"
	_ "github.com/metatube-community/metatube-sdk-go/provider/madouqu"
	_ "github.com/metatube-community/metatube-sdk-go/provider/mgstage"
	_ "github.com/metatube-community/metatube-sdk-go/provider/modelmediaasia"
	_ "github.com/metatube-community/metatube-sdk-go/provider/muramura"
	_ "github.com/metatube-community/metatube-sdk-go/provider/mywife"
	_ "github.com/metatube-community/metatube-sdk-go/provider/pacopacomama"
	_ "github.com/metatube-community/metatube-sdk-go/provider/pcolle"
	_ "github.com/metatube-community/metatube-sdk-go/provider/sod"
	_ "github.com/metatube-community/metatube-sdk-go/provider/theporndb"
	_ "github.com/metatube-community/metatube-sdk-go/provider/tokyo-hot"
)
