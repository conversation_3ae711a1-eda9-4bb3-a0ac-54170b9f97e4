# MetaTube SDK Docker 云部署指南

## 📋 概述

项目已经提供了完整的 Docker 配置，可以直接用于云部署。包含：
- `Dockerfile` - 构建 MetaTube 服务器镜像
- `docker-compose.yaml` - 完整的服务编排（包含 PostgreSQL 数据库）

## 🚀 快速部署

### 方法1: 使用官方镜像（推荐）

```bash
# 下载配置文件
wget https://raw.githubusercontent.com/metatube-community/metatube-sdk-go/main/docker-compose.yaml

# 启动服务
docker-compose up -d
```

### 方法2: 自己构建镜像

```bash
# 克隆项目
git clone https://github.com/metatube-community/metatube-sdk-go.git
cd metatube-sdk-go

# 构建并启动
docker-compose up -d --build
```

## ☁️ 云服务厂商部署

### 1. 阿里云 ECS

```bash
# 1. 创建 ECS 实例（推荐配置）
# - CPU: 2核心
# - 内存: 4GB
# - 存储: 40GB SSD
# - 操作系统: Ubuntu 20.04

# 2. 安装 Docker
curl -fsSL https://get.docker.com | bash
sudo systemctl start docker
sudo systemctl enable docker

# 3. 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 4. 部署服务
git clone https://github.com/metatube-community/metatube-sdk-go.git
cd metatube-sdk-go
sudo docker-compose up -d
```

### 2. 腾讯云 CVM

```bash
# 类似阿里云，推荐配置：
# - CPU: 2核心
# - 内存: 4GB  
# - 存储: 40GB 高性能云硬盘
# - 操作系统: Ubuntu 20.04

# 部署步骤同阿里云
```

### 3. AWS EC2

```bash
# 推荐实例类型: t3.medium
# - vCPU: 2
# - 内存: 4GB
# - 存储: 40GB gp3
# - AMI: Ubuntu 20.04 LTS

# 安装 Docker（Ubuntu）
sudo apt update
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker ubuntu

# 部署服务
git clone https://github.com/metatube-community/metatube-sdk-go.git
cd metatube-sdk-go
docker-compose up -d
```

### 4. Google Cloud Platform

```bash
# 推荐机器类型: e2-medium
# - vCPU: 1
# - 内存: 4GB
# - 存储: 40GB 标准永久磁盘
# - 操作系统: Ubuntu 20.04 LTS

# 使用 Container-Optimized OS 更简单
gcloud compute instances create metatube-server \
    --image-family=cos-stable \
    --image-project=cos-cloud \
    --machine-type=e2-medium \
    --boot-disk-size=40GB
```

## 🔧 自定义配置

### 1. 环境变量配置

创建 `.env` 文件：

```bash
# 基本配置
PORT=8080
TOKEN=your-secret-token-here
GIN_MODE=release

# 数据库配置
POSTGRES_USER=metatube
POSTGRES_PASSWORD=your-strong-password
POSTGRES_DB=metatube

# 代理配置（如果需要）
HTTP_PROXY=http://your-proxy:port
HTTPS_PROXY=http://your-proxy:port

# Provider 配置
MT_MOVIE_PROVIDER_FANZA__PRIORITY=2000
MT_MOVIE_PROVIDER_JAVBUS__PRIORITY=1500
MT_MOVIE_PROVIDER_FANZA__TIMEOUT=30s

# 数据库优化
DB_MAX_IDLE_CONNS=10
DB_MAX_OPEN_CONNS=100
DB_PREPARED_STMT=1
DB_AUTO_MIGRATE=1
```

### 2. 自定义 docker-compose.yaml

```yaml
version: '3.8'

services:
  metatube:
    image: ghcr.io/metatube-community/metatube-server:latest
    container_name: metatube
    ports:
      - "8080:8080"
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - PORT=8080
      - TOKEN=${TOKEN}
      - GIN_MODE=release
      - HTTP_PROXY=${HTTP_PROXY}
      - HTTPS_PROXY=${HTTPS_PROXY}
      - MT_MOVIE_PROVIDER_FANZA__PRIORITY=2000
      - MT_MOVIE_PROVIDER_JAVBUS__PRIORITY=1500
    volumes:
      - run:/var/run
    command: >
      -dsn "postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}?sslmode=disable"
      -port 8080
      -token ${TOKEN}
      -db-auto-migrate
      -db-prepared-stmt
      -db-max-open-conns 100
      -db-max-idle-conns 10
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15-alpine
    container_name: metatube-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - run:/var/run
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：添加 Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: metatube-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - metatube
    restart: unless-stopped

volumes:
  postgres_data:
  run:
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 2. SSL 证书配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 或使用 Docker 版本
docker run -it --rm \
  -v /etc/letsencrypt:/etc/letsencrypt \
  -p 80:80 \
  certbot/certbot certonly --standalone -d your-domain.com
```

### 3. Nginx 配置示例

```nginx
events {
    worker_connections 1024;
}

http {
    upstream metatube {
        server metatube:8080;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;

        location / {
            proxy_pass http://metatube;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## 📊 监控和日志

### 1. 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f metatube
docker-compose logs -f postgres

# 查看最近的日志
docker-compose logs --tail=100 metatube
```

### 2. 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看容器状态
docker-compose ps
```

## 🔄 维护和更新

### 1. 更新服务

```bash
# 拉取最新镜像
docker-compose pull

# 重启服务
docker-compose up -d

# 清理旧镜像
docker image prune -f
```

### 2. 备份数据库

```bash
# 备份
docker-compose exec postgres pg_dump -U metatube metatube > backup.sql

# 恢复
docker-compose exec -T postgres psql -U metatube metatube < backup.sql
```

## 💰 成本估算

### 云服务器配置建议：

| 云厂商 | 实例类型 | 配置 | 月费用（约） |
|--------|----------|------|-------------|
| 阿里云 | ecs.t6-c2m1.large | 2核4GB | ¥200-300 |
| 腾讯云 | S5.MEDIUM4 | 2核4GB | ¥180-280 |
| AWS | t3.medium | 2核4GB | $30-50 |
| GCP | e2-medium | 1核4GB | $25-40 |

### 流量费用：
- 国内云厂商：¥0.8-1.0/GB
- AWS/GCP：$0.09-0.12/GB

## 🎯 使用建议

1. **选择地区**：选择离目标用户最近的数据中心
2. **配置代理**：如果需要访问被限制的 provider，配置代理服务器
3. **定期备份**：设置自动备份数据库
4. **监控告警**：配置服务监控和告警
5. **安全更新**：定期更新系统和 Docker 镜像
