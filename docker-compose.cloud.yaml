version: '3.8'

services:
  metatube:
    image: ghcr.io/metatube-community/metatube-server:latest
    container_name: metatube
    ports:
      - "8080:8080"
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # 基本配置
      - PORT=8080
      - TOKEN=${TOKEN:-}
      - GIN_MODE=release
      
      # 代理配置（如果需要）
      - HTTP_PROXY=${HTTP_PROXY:-}
      - HTTPS_PROXY=${HTTPS_PROXY:-}
      
      # Provider 优先级配置
      - MT_MOVIE_PROVIDER_FANZA__PRIORITY=${FANZA_PRIORITY:-2000}
      - MT_MOVIE_PROVIDER_JAVBUS__PRIORITY=${JAVBUS_PRIORITY:-1500}
      - MT_MOVIE_PROVIDER_JAVDB__PRIORITY=${JAVDB_PRIORITY:-1400}
      - MT_MOVIE_PROVIDER_DAHLIA__PRIORITY=${DAHLIA_PRIORITY:-995}
      
      # Provider 超时配置
      - MT_MOVIE_PROVIDER_FANZA__TIMEOUT=${FANZA_TIMEOUT:-30s}
      - MT_MOVIE_PROVIDER_JAVBUS__TIMEOUT=${JAVBUS_TIMEOUT:-30s}
      
      # 数据库优化配置
      - DB_MAX_IDLE_CONNS=${DB_MAX_IDLE_CONNS:-10}
      - DB_MAX_OPEN_CONNS=${DB_MAX_OPEN_CONNS:-100}
      - DB_PREPARED_STMT=1
      - DB_AUTO_MIGRATE=1
      
    volumes:
      - run:/var/run
      - ./logs:/var/log/metatube
    command: >
      -dsn "postgres://${POSTGRES_USER:-metatube}:${POSTGRES_PASSWORD:-metatube}@postgres:5432/${POSTGRES_DB:-metatube}?sslmode=disable"
      -port 8080
      ${TOKEN:+-token ${TOKEN}}
      -db-auto-migrate
      -db-prepared-stmt
      -db-max-open-conns ${DB_MAX_OPEN_CONNS:-100}
      -db-max-idle-conns ${DB_MAX_IDLE_CONNS:-10}
      -request-timeout ${REQUEST_TIMEOUT:-60s}
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  postgres:
    image: postgres:15-alpine
    container_name: metatube-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-metatube}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-metatube}
      - POSTGRES_DB=${POSTGRES_DB:-metatube}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - run:/var/run
      - ./postgres-init:/docker-entrypoint-initdb.d
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c log_statement=mod
      -c log_min_duration_statement=1000
      -c log_line_prefix='%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-metatube} -d ${POSTGRES_DB:-metatube}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 可选：Redis 缓存（提升性能）
  redis:
    image: redis:7-alpine
    container_name: metatube-redis
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"
    profiles:
      - cache

  # 可选：Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: metatube-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      metatube:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    profiles:
      - proxy

  # 可选：监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: metatube-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: metatube-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  run:
    driver: local

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
