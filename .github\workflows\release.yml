name: Publish Go Releases

concurrency:
  group: release-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    tags:
      - '*'

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          check-latest: true
          go-version-file: 'go.mod'

      - name: Cache go module
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Build
        if: startsWith(github.ref, 'refs/tags/')
        run: make releases

      - name: Upload Releases
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          body: _Auto Released by Actions_
          files: build/*
          draft: false
          prerelease: false
          repository: metatube-community/metatube-server-releases
          token: ${{ secrets.ORG_PAT }}
