#!/bin/bash

# MetaTube Docker 镜像构建和推送脚本
# 支持推送到 Docker Hub、GitHub Container Registry、阿里云等

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
DEFAULT_REGISTRY="ghcr.io"
DEFAULT_NAMESPACE="your-username"
DEFAULT_IMAGE_NAME="metatube-server"
DEFAULT_TAG="latest"

# 显示使用说明
show_usage() {
    echo "MetaTube Docker 镜像构建和推送工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -r, --registry REGISTRY     镜像仓库地址 (默认: $DEFAULT_REGISTRY)"
    echo "  -n, --namespace NAMESPACE   命名空间/用户名 (默认: $DEFAULT_NAMESPACE)"
    echo "  -i, --image IMAGE           镜像名称 (默认: $DEFAULT_IMAGE_NAME)"
    echo "  -t, --tag TAG               镜像标签 (默认: $DEFAULT_TAG)"
    echo "  -p, --platform PLATFORM    目标平台 (默认: linux/amd64,linux/arm64)"
    echo "  --no-push                   只构建不推送"
    echo "  --no-cache                  不使用缓存构建"
    echo "  -h, --help                  显示此帮助信息"
    echo ""
    echo "支持的镜像仓库:"
    echo "  - Docker Hub: docker.io"
    echo "  - GitHub Container Registry: ghcr.io"
    echo "  - 阿里云容器镜像服务: registry.cn-hangzhou.aliyuncs.com"
    echo "  - 腾讯云容器镜像服务: ccr.ccs.tencentyun.com"
    echo ""
    echo "示例:"
    echo "  $0 -r docker.io -n myusername -i metatube -t v1.0.0"
    echo "  $0 -r ghcr.io -n myusername --no-push"
    echo "  $0 -r registry.cn-hangzhou.aliyuncs.com -n mynamespace"
}

# 解析命令行参数
REGISTRY="$DEFAULT_REGISTRY"
NAMESPACE="$DEFAULT_NAMESPACE"
IMAGE_NAME="$DEFAULT_IMAGE_NAME"
TAG="$DEFAULT_TAG"
PLATFORM="linux/amd64,linux/arm64"
PUSH=true
USE_CACHE=true

while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -i|--image)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        --no-push)
            PUSH=false
            shift
            ;;
        --no-cache)
            USE_CACHE=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 构建完整的镜像名称
FULL_IMAGE_NAME="$REGISTRY/$NAMESPACE/$IMAGE_NAME:$TAG"

log_info "开始构建 MetaTube Docker 镜像"
echo "配置信息:"
echo "  镜像仓库: $REGISTRY"
echo "  命名空间: $NAMESPACE"
echo "  镜像名称: $IMAGE_NAME"
echo "  镜像标签: $TAG"
echo "  完整名称: $FULL_IMAGE_NAME"
echo "  目标平台: $PLATFORM"
echo "  是否推送: $PUSH"
echo "  使用缓存: $USE_CACHE"
echo ""

# 检查 Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装或不在 PATH 中"
    exit 1
fi

# 检查 Docker Buildx
if ! docker buildx version &> /dev/null; then
    log_error "Docker Buildx 未安装，请升级 Docker 到最新版本"
    exit 1
fi

# 创建 buildx builder（如果不存在）
if ! docker buildx ls | grep -q "metatube-builder"; then
    log_info "创建 Docker Buildx builder..."
    docker buildx create --name metatube-builder --use
fi

# 构建参数
BUILD_ARGS=""
if [[ "$USE_CACHE" == "false" ]]; then
    BUILD_ARGS="$BUILD_ARGS --no-cache"
fi

if [[ "$PUSH" == "true" ]]; then
    BUILD_ARGS="$BUILD_ARGS --push"
else
    BUILD_ARGS="$BUILD_ARGS --load"
fi

# 登录到镜像仓库
if [[ "$PUSH" == "true" ]]; then
    log_info "登录到镜像仓库 $REGISTRY..."
    
    case $REGISTRY in
        "docker.io"|"registry-1.docker.io")
            echo "请输入 Docker Hub 用户名和密码"
            docker login
            ;;
        "ghcr.io")
            echo "请输入 GitHub 用户名和 Personal Access Token"
            docker login ghcr.io
            ;;
        "registry.cn-"*)
            echo "请输入阿里云容器镜像服务用户名和密码"
            docker login $REGISTRY
            ;;
        "ccr.ccs.tencentyun.com")
            echo "请输入腾讯云容器镜像服务用户名和密码"
            docker login $REGISTRY
            ;;
        *)
            echo "请输入 $REGISTRY 的用户名和密码"
            docker login $REGISTRY
            ;;
    esac
fi

# 构建镜像
log_info "开始构建镜像..."
docker buildx build \
    --platform $PLATFORM \
    --tag $FULL_IMAGE_NAME \
    $BUILD_ARGS \
    .

if [[ $? -eq 0 ]]; then
    log_success "镜像构建成功！"
    
    if [[ "$PUSH" == "true" ]]; then
        log_success "镜像已推送到 $REGISTRY"
    else
        log_success "镜像已构建到本地"
    fi
    
    echo ""
    echo "🚀 云平台部署配置:"
    echo "================================"
    echo "Image Name: $FULL_IMAGE_NAME"
    echo "Container Port: 8080"
    echo "Command: -port 8080 -db-auto-migrate"
    echo ""
    echo "Environment Variables (可选):"
    echo "  TOKEN=your-secret-token"
    echo "  DSN=sqlite:///data/metatube.db"
    echo "  HTTP_PROXY=http://your-proxy:port"
    echo "  HTTPS_PROXY=http://your-proxy:port"
    echo ""
    echo "Network:"
    echo "  Public Access: 启用"
    echo "  Container Port: 8080"
    echo "  Public Port: 80 (或其他)"
    echo ""
    
    # 生成不同云平台的配置示例
    echo "📋 不同云平台配置示例:"
    echo ""
    echo "Railway:"
    echo "  Image: $FULL_IMAGE_NAME"
    echo "  Port: 8080"
    echo "  Start Command: -port 8080 -db-auto-migrate"
    echo ""
    echo "Render:"
    echo "  Image URL: $FULL_IMAGE_NAME"
    echo "  Port: 8080"
    echo "  Start Command: -port 8080 -db-auto-migrate"
    echo ""
    echo "Fly.io:"
    echo "  Image: $FULL_IMAGE_NAME"
    echo "  Internal Port: 8080"
    echo "  CMD: [\"-port\", \"8080\", \"-db-auto-migrate\"]"
    
else
    log_error "镜像构建失败！"
    exit 1
fi
