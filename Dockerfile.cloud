# MetaTube Server - 云平台优化版 Dockerfile
# 针对 Railway、Render、Fly.io 等云平台优化

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache git make ca-certificates tzdata

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build \
    -a -installsuffix cgo \
    -ldflags '-w -s -extldflags "-static"' \
    -o metatube-server \
    ./cmd/server

# 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata && \
    addgroup -g 1001 -S metatube && \
    adduser -u 1001 -S metatube -G metatube

# 设置工作目录
WORKDIR /app

# 创建数据目录
RUN mkdir -p /data && chown metatube:metatube /data

# 从构建阶段复制二进制文件
COPY --from=builder /app/metatube-server .
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# 设置权限
RUN chmod +x metatube-server && \
    chown metatube:metatube metatube-server

# 切换到非 root 用户
USER metatube

# 设置环境变量
ENV GIN_MODE=release
ENV PORT=8080
ENV DSN="sqlite:///data/metatube.db"

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/ || exit 1

# 启动命令
ENTRYPOINT ["./metatube-server"]
CMD ["-port", "8080", "-db-auto-migrate", "-dsn", "sqlite:///data/metatube.db"]
