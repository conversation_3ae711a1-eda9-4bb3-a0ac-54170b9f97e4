## Contributing

- With issues:
    - Use the search tool before opening a new issue.
    - Please provide source code and commit sha if you found a bug.
    - Review existing issues and provide feedback or react to them.

- With pull requests:
    - Open your pull request against `main`
    - It should pass all tests in the available continuous integration systems such as GitHub Actions.
    - You should add/modify tests to cover your proposed code changes.
