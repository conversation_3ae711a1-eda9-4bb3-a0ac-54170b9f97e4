#!/usr/bin/env python3
"""
MetaTube 云部署测试脚本
用于验证部署是否成功
"""

import requests
import time
import sys
import json
from urllib.parse import urljoin


class DeploymentTester:
    def __init__(self, base_url="http://localhost:8080", token=None):
        self.base_url = base_url.rstrip('/')
        self.token = token
        self.session = requests.Session()
        
        if self.token:
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
    
    def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                print("✅ 健康检查通过")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_providers_api(self):
        """测试 Providers API"""
        print("🔍 测试 Providers API...")
        try:
            response = self.session.get(f"{self.base_url}/v1/providers", timeout=10)
            if response.status_code == 200:
                data = response.json()
                movie_providers = data.get('data', {}).get('movie', {})
                actor_providers = data.get('data', {}).get('actor', {})
                
                print(f"✅ Providers API 正常")
                print(f"   Movie Providers: {len(movie_providers)} 个")
                print(f"   Actor Providers: {len(actor_providers)} 个")
                
                # 显示前几个 provider
                if movie_providers:
                    print("   主要 Movie Providers:")
                    for name, info in list(movie_providers.items())[:5]:
                        priority = info.get('priority', 'N/A')
                        print(f"     - {name} (优先级: {priority})")
                
                return True
            else:
                print(f"❌ Providers API 失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Providers API 异常: {e}")
            return False
    
    def test_search_api(self):
        """测试搜索 API"""
        print("🔍 测试搜索 API...")
        try:
            # 测试一个常见的番号
            response = self.session.get(
                f"{self.base_url}/v1/movies/search",
                params={'q': 'abp-178'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('data', [])
                print(f"✅ 搜索 API 正常")
                print(f"   搜索 'abp-178' 返回 {len(results)} 个结果")
                
                if results:
                    first_result = results[0]
                    print(f"   第一个结果:")
                    print(f"     标题: {first_result.get('title', 'N/A')}")
                    print(f"     Provider: {first_result.get('provider', 'N/A')}")
                    print(f"     ID: {first_result.get('id', 'N/A')}")
                
                return True
            else:
                print(f"❌ 搜索 API 失败: HTTP {response.status_code}")
                if response.status_code == 401:
                    print("   可能需要设置 TOKEN 认证")
                return False
        except Exception as e:
            print(f"❌ 搜索 API 异常: {e}")
            return False
    
    def test_movie_info_api(self):
        """测试影片信息 API"""
        print("🔍 测试影片信息 API...")
        try:
            # 先搜索获取一个有效的 provider 和 ID
            search_response = self.session.get(
                f"{self.base_url}/v1/movies/search",
                params={'q': 'abp-178'},
                timeout=30
            )
            
            if search_response.status_code != 200:
                print("⚠️  无法获取搜索结果，跳过影片信息测试")
                return True
            
            results = search_response.json().get('data', [])
            if not results:
                print("⚠️  搜索无结果，跳过影片信息测试")
                return True
            
            # 使用第一个结果测试详细信息 API
            first_result = results[0]
            provider = first_result.get('provider')
            movie_id = first_result.get('id')
            
            if not provider or not movie_id:
                print("⚠️  搜索结果缺少必要信息，跳过影片信息测试")
                return True
            
            response = self.session.get(
                f"{self.base_url}/v1/movies/{provider}/{movie_id}",
                params={'lazy': 'true'},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                info = data.get('data', {})
                print(f"✅ 影片信息 API 正常")
                print(f"   获取 {provider}/{movie_id} 详细信息成功")
                print(f"   标题: {info.get('title', 'N/A')}")
                print(f"   发行日期: {info.get('release_date', 'N/A')}")
                return True
            else:
                print(f"❌ 影片信息 API 失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 影片信息 API 异常: {e}")
            return False
    
    def test_performance(self):
        """测试性能"""
        print("🔍 测试性能...")
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/v1/providers", timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = (end_time - start_time) * 1000
                print(f"✅ 性能测试通过")
                print(f"   响应时间: {response_time:.2f}ms")
                
                if response_time < 1000:
                    print("   🚀 响应速度优秀")
                elif response_time < 3000:
                    print("   👍 响应速度良好")
                else:
                    print("   ⚠️  响应速度较慢，可能需要优化")
                
                return True
            else:
                print(f"❌ 性能测试失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 性能测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 MetaTube 云部署测试")
        print("=" * 50)
        print(f"测试目标: {self.base_url}")
        print()
        
        tests = [
            ("健康检查", self.test_health_check),
            ("Providers API", self.test_providers_api),
            ("搜索 API", self.test_search_api),
            ("影片信息 API", self.test_movie_info_api),
            ("性能测试", self.test_performance),
        ]
        
        passed = 0
        total = len(tests)
        
        for name, test_func in tests:
            print(f"📋 {name}")
            try:
                if test_func():
                    passed += 1
                print()
            except Exception as e:
                print(f"❌ {name} 测试异常: {e}")
                print()
        
        print("=" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！部署成功！")
            print()
            print("🚀 现在可以使用以下方式访问 MetaTube:")
            print(f"   - API 地址: {self.base_url}")
            print(f"   - Python 客户端: python metatube_client.py --url {self.base_url} search 'abp-178'")
            if self.token:
                print(f"   - 认证令牌: {self.token}")
            return True
        else:
            print("⚠️  部分测试失败，请检查部署配置")
            print()
            print("🔧 故障排除建议:")
            print("   1. 检查服务是否正常运行: docker-compose ps")
            print("   2. 查看服务日志: docker-compose logs -f")
            print("   3. 检查防火墙设置")
            print("   4. 验证网络连接")
            return False


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="MetaTube 云部署测试工具")
    parser.add_argument("--url", default="http://localhost:8080", help="MetaTube 服务地址")
    parser.add_argument("--token", help="API 认证令牌")
    parser.add_argument("--quick", action="store_true", help="快速测试（仅健康检查）")
    
    args = parser.parse_args()
    
    tester = DeploymentTester(args.url, args.token)
    
    if args.quick:
        # 快速测试
        if tester.test_health_check():
            print("🎉 快速测试通过！服务正常运行")
            sys.exit(0)
        else:
            print("❌ 快速测试失败！服务可能未正常启动")
            sys.exit(1)
    else:
        # 完整测试
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
