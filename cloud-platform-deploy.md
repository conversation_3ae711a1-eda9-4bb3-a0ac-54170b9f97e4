# 云平台容器部署指南

## 📋 概述

本指南适用于支持容器部署的云平台，如 Railway、Render、Fly.io、<PERSON>ku 等。这些平台通常需要指定镜像名称、端口和启动命令。

## 🐳 第一步：准备镜像

### 方法1: 使用官方镜像（推荐）

```
镜像名称: ghcr.io/metatube-community/metatube-server:latest
```

### 方法2: 构建自己的镜像

```bash
# 克隆项目
git clone https://github.com/metatube-community/metatube-sdk-go.git
cd metatube-sdk-go

# 构建并推送到 Docker Hub
chmod +x build-and-push.sh
./build-and-push.sh -r docker.io -n your-username -i metatube-server

# 或推送到 GitHub Container Registry
./build-and-push.sh -r ghcr.io -n your-username -i metatube-server
```

## 🚀 第二步：云平台部署配置

### Railway 部署

1. **基本配置**
   ```
   Image: ghcr.io/metatube-community/metatube-server:latest
   Port: 8080
   ```

2. **启动命令**
   ```
   -port 8080 -db-auto-migrate
   ```

3. **环境变量**
   ```
   TOKEN=your-secret-token-here
   DSN=sqlite:///data/metatube.db
   GIN_MODE=release
   ```

4. **网络配置**
   ```
   Container Port: 8080
   Public Access: 启用
   ```

### Render 部署

1. **服务配置**
   ```
   Type: Web Service
   Image URL: ghcr.io/metatube-community/metatube-server:latest
   Port: 8080
   ```

2. **启动命令**
   ```
   -port 8080 -db-auto-migrate -dsn sqlite:///data/metatube.db
   ```

3. **环境变量**
   ```
   TOKEN=your-secret-token-here
   GIN_MODE=release
   PORT=8080
   ```

### Fly.io 部署

1. **fly.toml 配置**
   ```toml
   app = "metatube-app"
   primary_region = "nrt"

   [build]
   image = "ghcr.io/metatube-community/metatube-server:latest"

   [http_service]
   internal_port = 8080
   force_https = true

   [[http_service.checks]]
   grace_period = "10s"
   interval = "30s"
   method = "GET"
   path = "/"
   timeout = "5s"

   [env]
   GIN_MODE = "release"
   PORT = "8080"

   [[mounts]]
   source = "metatube_data"
   destination = "/data"
   ```

2. **部署命令**
   ```bash
   fly deploy
   ```

### Heroku 部署

1. **heroku.yml 配置**
   ```yaml
   build:
     docker:
       web: Dockerfile
   run:
     web: /metatube-server -port $PORT -db-auto-migrate
   ```

2. **环境变量**
   ```bash
   heroku config:set TOKEN=your-secret-token-here
   heroku config:set GIN_MODE=release
   ```

### DigitalOcean App Platform

1. **应用配置**
   ```yaml
   name: metatube-app
   services:
   - name: web
     image:
       registry_type: DOCKER_HUB
       registry: ghcr.io
       repository: metatube-community/metatube-server
       tag: latest
     instance_count: 1
     instance_size_slug: basic-xxs
     http_port: 8080
     run_command: "-port 8080 -db-auto-migrate"
     environment_slug: node-js
     envs:
     - key: TOKEN
       value: your-secret-token-here
     - key: GIN_MODE
       value: release
   ```

## 🔧 通用配置参数

### 必需配置

| 参数 | 值 | 说明 |
|------|----|----|
| Image | `ghcr.io/metatube-community/metatube-server:latest` | 镜像地址 |
| Port | `8080` | 容器端口 |
| Command | `-port 8080 -db-auto-migrate` | 启动命令 |

### 推荐环境变量

| 变量名 | 示例值 | 说明 |
|--------|--------|------|
| `TOKEN` | `your-secret-token` | API 认证令牌 |
| `GIN_MODE` | `release` | 运行模式 |
| `DSN` | `sqlite:///data/metatube.db` | 数据库连接 |
| `HTTP_PROXY` | `http://proxy:port` | HTTP 代理（可选） |
| `HTTPS_PROXY` | `http://proxy:port` | HTTPS 代理（可选） |

### 高级配置

```bash
# 完整启动命令示例
-port 8080 -token your-secret-token -db-auto-migrate -dsn sqlite:///data/metatube.db -request-timeout 60s

# Provider 优先级配置
MT_MOVIE_PROVIDER_FANZA__PRIORITY=2000
MT_MOVIE_PROVIDER_JAVBUS__PRIORITY=1500
MT_MOVIE_PROVIDER_FANZA__TIMEOUT=30s
```

## 💾 数据持久化

### SQLite 数据库（推荐）

大多数云平台支持临时存储，适合使用 SQLite：

```bash
# 启动命令
-port 8080 -db-auto-migrate -dsn "sqlite:///data/metatube.db"
```

### PostgreSQL 数据库

如果平台提供 PostgreSQL 服务：

```bash
# 启动命令
-port 8080 -db-auto-migrate -dsn "******************************/dbname?sslmode=require"
```

## 🌐 网络和域名

### 自定义域名

大多数平台支持绑定自定义域名：

1. 在平台控制台添加域名
2. 配置 DNS CNAME 记录
3. 启用 HTTPS（通常自动）

### CORS 配置

如果需要跨域访问，添加环境变量：

```
ALLOWED_ORIGINS=https://your-frontend-domain.com
```

## 🔒 安全配置

### 1. 设置强密码令牌

```bash
# 生成随机令牌
openssl rand -hex 32
```

### 2. 启用 HTTPS

大多数云平台默认提供 HTTPS，确保启用。

### 3. 限制访问

```bash
# 如果支持，配置 IP 白名单或 VPN 访问
```

## 📊 监控和日志

### 查看日志

```bash
# Railway
railway logs

# Render
# 在控制台查看日志

# Fly.io
fly logs

# Heroku
heroku logs --tail
```

### 健康检查

大多数平台支持健康检查：

```
Health Check URL: /
Expected Status: 200
```

## 💰 成本估算

| 平台 | 免费额度 | 付费价格 | 特点 |
|------|----------|----------|------|
| Railway | $5/月 | $0.000463/GB-hour | 按使用量计费 |
| Render | 750小时/月 | $7/月起 | 固定价格 |
| Fly.io | 3个应用免费 | $1.94/月起 | 按资源计费 |
| Heroku | 1000小时/月 | $7/月起 | 经典平台 |

## 🧪 部署验证

部署完成后，使用测试脚本验证：

```bash
# 下载测试脚本
wget https://raw.githubusercontent.com/your-repo/test_deployment.py

# 测试部署
python test_deployment.py --url https://your-app.railway.app

# 测试搜索功能
curl "https://your-app.railway.app/v1/movies/search?q=abp-178"
```

## 🔄 更新和维护

### 自动更新

设置 GitHub Actions 自动构建和部署：

```yaml
# .github/workflows/deploy.yml
name: Deploy to Railway
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Deploy to Railway
      run: |
        # 触发 Railway 重新部署
        curl -X POST ${{ secrets.RAILWAY_WEBHOOK_URL }}
```

### 手动更新

```bash
# 重新构建镜像
./build-and-push.sh

# 在云平台触发重新部署
```

## 🎯 最佳实践

1. **选择合适的地区**：选择离用户最近的数据中心
2. **配置代理**：如果需要访问被限制的网站
3. **监控资源使用**：避免超出免费额度
4. **定期备份**：导出重要数据
5. **安全配置**：使用强密码和 HTTPS

## 🆘 故障排除

### 常见问题

1. **启动失败**
   - 检查镜像名称是否正确
   - 验证启动命令语法
   - 查看平台日志

2. **无法访问**
   - 确认端口配置为 8080
   - 检查防火墙设置
   - 验证域名解析

3. **性能问题**
   - 增加实例规格
   - 配置数据库连接池
   - 启用缓存

### 获取帮助

- 查看平台文档
- 检查 GitHub Issues
- 联系技术支持
