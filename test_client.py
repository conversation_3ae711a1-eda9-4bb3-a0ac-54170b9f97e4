#!/usr/bin/env python3
"""
MetaTube 客户端测试脚本
用于验证客户端功能是否正常工作
"""

import time
import subprocess
import sys
from metatube_client import MetaTubeClient, MetaTubeServer


def test_server_management():
    """测试服务器启动和停止"""
    print("=== 测试服务器管理 ===")
    
    server = MetaTubeServer()
    
    try:
        print("启动服务器...")
        server.start(port=8081)  # 使用不同端口避免冲突
        
        print("检查服务器状态...")
        assert server.is_running(), "服务器应该在运行中"
        
        print("停止服务器...")
        server.stop()
        
        time.sleep(2)  # 等待服务器完全停止
        assert not server.is_running(), "服务器应该已停止"
        
        print("✅ 服务器管理测试通过")
        
    except Exception as e:
        print(f"❌ 服务器管理测试失败: {e}")
        server.stop()  # 确保清理
        return False
    
    return True


def test_client_functionality():
    """测试客户端功能"""
    print("\n=== 测试客户端功能 ===")
    
    server = MetaTubeServer()
    client = MetaTubeClient("http://localhost:8081")
    
    try:
        # 启动服务器
        print("启动测试服务器...")
        server.start(port=8081)
        
        # 测试健康检查
        print("测试健康检查...")
        assert client.check_health(), "服务器健康检查失败"
        print("✅ 健康检查通过")
        
        # 测试获取 providers
        print("测试获取 providers...")
        providers = client.get_providers()
        assert isinstance(providers, dict), "Providers 应该返回字典"
        assert 'movie' in providers or 'actor' in providers, "应该包含 movie 或 actor providers"
        print(f"✅ 获取到 {len(providers.get('movie', {}))} 个 movie providers")
        
        # 测试搜索功能
        print("测试搜索功能...")
        try:
            results = client.search_movie("abp-178")
            print(f"✅ 搜索返回 {len(results)} 个结果")
            
            # 如果有结果，测试获取详细信息
            if results:
                first_result = results[0]
                provider = first_result.get('provider')
                movie_id = first_result.get('id')
                
                if provider and movie_id:
                    print(f"测试获取详细信息: {provider}/{movie_id}")
                    info = client.get_movie_info(provider, movie_id)
                    assert isinstance(info, dict), "影片信息应该返回字典"
                    print("✅ 获取详细信息成功")
            
        except Exception as e:
            print(f"⚠️  搜索测试失败（可能是网络问题）: {e}")
        
        print("✅ 客户端功能测试完成")
        
    except Exception as e:
        print(f"❌ 客户端功能测试失败: {e}")
        return False
    
    finally:
        server.stop()
    
    return True


def test_command_line_interface():
    """测试命令行接口"""
    print("\n=== 测试命令行接口 ===")
    
    try:
        # 测试帮助信息
        result = subprocess.run([
            sys.executable, "metatube_client.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        assert result.returncode == 0, "帮助命令应该成功执行"
        assert "MetaTube SDK Go 客户端工具" in result.stdout, "应该包含帮助信息"
        print("✅ 帮助信息测试通过")
        
        # 测试 providers 命令（需要服务器运行）
        print("启动服务器进行 CLI 测试...")
        server = MetaTubeServer()
        server.start(port=8082)
        
        try:
            result = subprocess.run([
                sys.executable, "metatube_client.py", 
                "--url", "http://localhost:8082",
                "providers"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ CLI providers 命令测试通过")
            else:
                print(f"⚠️  CLI providers 命令失败: {result.stderr}")
        
        finally:
            server.stop()
        
        print("✅ 命令行接口测试完成")
        
    except Exception as e:
        print(f"❌ 命令行接口测试失败: {e}")
        return False
    
    return True


def run_integration_test():
    """运行集成测试"""
    print("=== MetaTube 客户端集成测试 ===\n")
    
    tests = [
        test_server_management,
        test_client_functionality,
        test_command_line_interface
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查问题")
        return False


def demo_usage():
    """演示基本用法"""
    print("\n=== 使用演示 ===")
    
    server = MetaTubeServer()
    
    try:
        print("1. 启动服务器...")
        server.start(port=8083)
        
        client = MetaTubeClient("http://localhost:8083")
        
        print("2. 获取可用 providers...")
        providers = client.get_providers()
        movie_providers = list(providers.get('movie', {}).keys())
        print(f"   可用的 movie providers: {movie_providers[:5]}...")  # 只显示前5个
        
        print("3. 搜索影片 'abp-178'...")
        results = client.search_movie("abp-178")
        print(f"   找到 {len(results)} 个结果")
        
        if results:
            first = results[0]
            print(f"   第一个结果: {first.get('title', 'N/A')}")
            print(f"   Provider: {first.get('provider', 'N/A')}")
            print(f"   ID: {first.get('id', 'N/A')}")
            
            # 获取详细信息
            if first.get('provider') and first.get('id'):
                print("4. 获取详细信息...")
                info = client.get_movie_info(first['provider'], first['id'])
                print(f"   标题: {info.get('title', 'N/A')}")
                print(f"   发行日期: {info.get('release_date', 'N/A')}")
                print(f"   时长: {info.get('runtime', 'N/A')} 分钟")
        
        print("\n✅ 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
    
    finally:
        server.stop()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MetaTube 客户端测试工具")
    parser.add_argument("--test", action="store_true", help="运行集成测试")
    parser.add_argument("--demo", action="store_true", help="运行使用演示")
    
    args = parser.parse_args()
    
    if args.test:
        success = run_integration_test()
        sys.exit(0 if success else 1)
    elif args.demo:
        demo_usage()
    else:
        print("请选择操作:")
        print("  --test  运行集成测试")
        print("  --demo  运行使用演示")
        print("\n或者直接使用 metatube_client.py 进行操作")
