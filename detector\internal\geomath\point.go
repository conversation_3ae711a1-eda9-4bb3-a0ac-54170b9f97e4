package geomath

import (
	"math"
)

/*
 * These functions were generated by ChatGPT-o1.
 */

func rotateFloat(x, y, rad float64) (float64, float64) {
	cosVal := math.Cos(rad)
	sinVal := math.Sin(rad)
	xNew := x*cosVal + y*sinVal
	yNew := -x*sinVal + y*cosVal
	return xNew, yNew
}

func RotatePoint(x, y int, width, height int, angle float64 /* in degrees */) (int, int) {
	if width <= 0 || height <= 0 {
		return 0, 0
	}

	rad := angle * math.Pi / 180.0

	w := float64(width - 1)
	h := float64(height - 1)
	corners := [][2]float64{
		{0, 0},
		{w, 0},
		{w, h},
		{0, h},
	}

	rx0, ry0 := rotateFloat(corners[0][0], corners[0][1], rad)
	minX, maxX := rx0, rx0
	minY, maxY := ry0, ry0

	for i := 1; i < len(corners); i++ {
		rx, ry := rotateFloat(corners[i][0], corners[i][1], rad)
		if rx < minX {
			minX = rx
		}
		if rx > maxX {
			maxX = rx
		}
		if ry < minY {
			minY = ry
		}
		if ry > maxY {
			maxY = ry
		}
	}

	newWidthF := math.Round(maxX - minX + 1)
	newHeightF := math.Round(maxY - minY + 1)
	newW := int(newWidthF)
	newH := int(newHeightF)

	rx, ry := rotateFloat(float64(x), float64(y), rad)

	rxShift := rx - minX
	ryShift := ry - minY

	ix := int(math.Round(rxShift))
	iy := int(math.Round(ryShift))

	if ix < 0 {
		ix = 0
	} else if ix >= newW {
		ix = newW - 1
	}
	if iy < 0 {
		iy = 0
	} else if iy >= newH {
		iy = newH - 1
	}

	return ix, iy
}
