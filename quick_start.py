#!/usr/bin/env python3
"""
MetaTube 快速启动脚本
一键搜索 abp-178 的完整示例
"""

import time
from metatube_client import MetaTubeClient, MetaTubeServer


def quick_search_abp178(http_proxy=None, https_proxy=None):
    """快速搜索 abp-178 示例"""
    print("🚀 MetaTube 快速搜索演示")
    print("=" * 50)

    server = MetaTubeServer()

    try:
        # 启动服务器
        print("📡 正在启动 MetaTube 服务器...")
        if http_proxy or https_proxy:
            print(f"🌐 使用代理设置:")
            if http_proxy:
                print(f"   HTTP: {http_proxy}")
            if https_proxy:
                print(f"   HTTPS: {https_proxy}")

        server.start(port=8080, http_proxy=http_proxy, https_proxy=https_proxy)

        # 创建客户端
        client = MetaTubeClient("http://localhost:8080")

        # 等待服务器完全启动
        print("⏳ 等待服务器就绪...")
        time.sleep(2)

        # 检查服务器状态
        if not client.check_health():
            print("❌ 服务器启动失败")
            return

        print("✅ 服务器启动成功！")
        print()

        # 获取可用 providers
        print("📋 获取可用的 Providers...")
        try:
            providers = client.get_providers()
            movie_providers = providers.get('movie', {})

            print(f"   找到 {len(movie_providers)} 个 Movie Providers:")
            for name, info in list(movie_providers.items())[:5]:  # 只显示前5个
                priority = info.get('priority', 'N/A')
                print(f"   - {name} (优先级: {priority})")

            if len(movie_providers) > 5:
                print(f"   ... 还有 {len(movie_providers) - 5} 个")
            print()

        except Exception as e:
            print(f"⚠️  获取 Providers 失败: {e}")
            print()

        # 搜索 abp-178
        print("🔍 搜索影片: abp-178")
        print("-" * 30)

        try:
            results = client.search_movie("abp-178", show_failures=True)

            if not results:
                print("❌ 未找到相关影片")
                print("💡 提示: 可能是网络问题或地区限制")
                return

            print(f"✅ 找到 {len(results)} 个结果:")
            print()

            # 显示搜索结果
            for i, movie in enumerate(results[:3], 1):  # 只显示前3个
                print(f"📺 结果 {i}:")
                print(f"   标题: {movie.get('title', 'N/A')}")
                print(f"   番号: {movie.get('number', 'N/A')}")
                print(f"   Provider: {movie.get('provider', 'N/A')}")
                print(f"   ID: {movie.get('id', 'N/A')}")
                print(f"   评分: {movie.get('score', 'N/A')}")
                print(f"   发行日期: {movie.get('release_date', 'N/A')}")

                if movie.get('actors'):
                    actors = ', '.join(movie['actors'][:2])  # 只显示前2个演员
                    if len(movie['actors']) > 2:
                        actors += f" 等{len(movie['actors'])}人"
                    print(f"   演员: {actors}")

                print(f"   链接: {movie.get('homepage', 'N/A')}")
                print()

            # 获取第一个结果的详细信息
            if results:
                first_result = results[0]
                provider = first_result.get('provider')
                movie_id = first_result.get('id')

                if provider and movie_id:
                    print("🎬 获取详细信息...")
                    print("-" * 30)

                    try:
                        info = client.get_movie_info(provider, movie_id)

                        print(f"🎭 {info.get('title', 'N/A')}")
                        print(f"   番号: {info.get('number', 'N/A')}")
                        print(f"   Provider: {info.get('provider', 'N/A')}")
                        print(f"   评分: {info.get('score', 'N/A')}")
                        print(f"   时长: {info.get('runtime', 'N/A')} 分钟")
                        print(f"   发行日期: {info.get('release_date', 'N/A')}")
                        print(f"   制作商: {info.get('maker', 'N/A')}")
                        print(f"   发行商: {info.get('label', 'N/A')}")
                        print(f"   系列: {info.get('series', 'N/A')}")
                        print(f"   导演: {info.get('director', 'N/A')}")

                        if info.get('actors'):
                            print(f"   演员: {', '.join(info['actors'])}")

                        if info.get('genres'):
                            print(f"   类型: {', '.join(info['genres'])}")

                        if info.get('summary'):
                            summary = info['summary'][:100]
                            if len(info['summary']) > 100:
                                summary += "..."
                            print(f"   简介: {summary}")

                        print(f"   封面: {info.get('cover_url', 'N/A')}")
                        print(f"   链接: {info.get('homepage', 'N/A')}")

                    except Exception as e:
                        print(f"⚠️  获取详细信息失败: {e}")

        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            print("💡 可能的原因:")
            print("   - 网络连接问题")
            print("   - 地区访问限制")
            print("   - Provider 服务不可用")

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保:")
        print("   - 已安装 Go 环境")
        print("   - 项目已正确构建")
        print("   - 端口 8080 未被占用")

    finally:
        print()
        print("🛑 正在停止服务器...")
        server.stop()
        print("✅ 演示完成！")


def show_usage_examples():
    """显示使用示例"""
    print("📚 MetaTube 客户端使用示例")
    print("=" * 50)
    print()

    examples = [
        ("搜索影片", "python metatube_client.py search 'abp-178'"),
        ("指定 Provider 搜索", "python metatube_client.py search 'abp-178' --provider fanza"),
        ("获取详细信息", "python metatube_client.py info fanza abp178"),
        ("搜索演员", "python metatube_client.py actor-search '桃乃木かな'"),
        ("查看 Providers", "python metatube_client.py providers"),
        ("启动服务器", "python metatube_client.py server --port 8080"),
        ("带认证启动", "python metatube_client.py server --token your-secret"),
        ("连接远程服务器", "python metatube_client.py --url http://remote:8080 search 'abp-178'"),
    ]

    for desc, cmd in examples:
        print(f"📌 {desc}:")
        print(f"   {cmd}")
        print()

    print("💡 更多信息请查看 README_client.md")


def main():
    import argparse

    parser = argparse.ArgumentParser(description="MetaTube 快速搜索演示")
    parser.add_argument("action", nargs="?", default="search",
                       choices=["search", "examples", "help"],
                       help="要执行的操作")
    parser.add_argument("--http-proxy", help="HTTP代理地址")
    parser.add_argument("--https-proxy", help="HTTPS代理地址")

    args = parser.parse_args()

    if args.action == "examples":
        show_usage_examples()
    elif args.action == "help":
        print("用法:")
        print("  python quick_start.py [search]     # 运行快速搜索演示")
        print("  python quick_start.py examples     # 显示使用示例")
        print("  python quick_start.py help         # 显示此帮助")
        print()
        print("代理选项:")
        print("  --http-proxy URL    设置HTTP代理")
        print("  --https-proxy URL   设置HTTPS代理")
        print()
        print("示例:")
        print("  python quick_start.py --http-proxy http://127.0.0.1:7890")
    else:
        # 默认运行快速搜索
        quick_search_abp178(args.http_proxy, args.https_proxy)


if __name__ == "__main__":
    main()
