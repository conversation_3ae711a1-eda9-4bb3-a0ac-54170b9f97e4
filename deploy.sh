#!/bin/bash

# MetaTube SDK 一键云部署脚本
# 支持阿里云、腾讯云、AWS、GCP 等主流云服务商

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到 root 用户，建议使用普通用户运行"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    log_info "检测到操作系统: $OS $VER"
}

# 安装 Docker
install_docker() {
    log_info "检查 Docker 安装状态..."
    
    if command -v docker &> /dev/null; then
        log_success "Docker 已安装"
        return
    fi
    
    log_info "安装 Docker..."
    
    case $OS in
        "Ubuntu"|"Debian GNU/Linux")
            sudo apt-get update
            sudo apt-get install -y ca-certificates curl gnupg lsb-release
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        "CentOS Linux"|"Red Hat Enterprise Linux")
            sudo yum install -y yum-utils
            sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        *)
            log_error "不支持的操作系统: $OS"
            exit 1
            ;;
    esac
    
    sudo systemctl start docker
    sudo systemctl enable docker
    sudo usermod -aG docker $USER
    
    log_success "Docker 安装完成"
}

# 安装 Docker Compose
install_docker_compose() {
    log_info "检查 Docker Compose 安装状态..."
    
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose 已安装"
        return
    fi
    
    log_info "安装 Docker Compose..."
    
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    log_success "Docker Compose 安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        sudo ufw allow 22/tcp
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        sudo ufw --force enable
    elif command -v firewall-cmd &> /dev/null; then
        sudo firewall-cmd --permanent --add-port=22/tcp
        sudo firewall-cmd --permanent --add-port=80/tcp
        sudo firewall-cmd --permanent --add-port=443/tcp
        sudo firewall-cmd --reload
    fi
    
    log_success "防火墙配置完成"
}

# 创建项目目录
setup_project() {
    log_info "设置项目目录..."
    
    PROJECT_DIR="$HOME/metatube"
    mkdir -p $PROJECT_DIR
    cd $PROJECT_DIR
    
    # 下载配置文件
    if [[ ! -f docker-compose.cloud.yaml ]]; then
        log_info "下载 Docker Compose 配置..."
        curl -fsSL https://raw.githubusercontent.com/metatube-community/metatube-sdk-go/main/docker-compose.yaml -o docker-compose.yaml
        # 如果有自定义的云配置，使用自定义的
        if [[ -f ../docker-compose.cloud.yaml ]]; then
            cp ../docker-compose.cloud.yaml ./
        fi
    fi
    
    # 创建环境变量文件
    if [[ ! -f .env ]]; then
        log_info "创建环境变量配置..."
        cat > .env << EOF
# MetaTube 配置
TOKEN=$(openssl rand -hex 32)
POSTGRES_PASSWORD=$(openssl rand -hex 16)
POSTGRES_USER=metatube
POSTGRES_DB=metatube

# 数据库优化
DB_MAX_IDLE_CONNS=10
DB_MAX_OPEN_CONNS=100

# Provider 配置
FANZA_PRIORITY=2000
JAVBUS_PRIORITY=1500
JAVDB_PRIORITY=1400

# 时区
TZ=Asia/Shanghai
EOF
        log_success "环境变量配置已创建"
    fi
    
    # 创建必要的目录
    mkdir -p logs nginx/ssl postgres-init backups
    
    log_success "项目目录设置完成: $PROJECT_DIR"
}

# 配置代理（可选）
configure_proxy() {
    read -p "是否需要配置代理？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -p "请输入 HTTP 代理地址 (例如: http://127.0.0.1:7890): " HTTP_PROXY
        read -p "请输入 HTTPS 代理地址 (例如: http://127.0.0.1:7890): " HTTPS_PROXY
        
        if [[ -n "$HTTP_PROXY" ]]; then
            echo "HTTP_PROXY=$HTTP_PROXY" >> .env
        fi
        if [[ -n "$HTTPS_PROXY" ]]; then
            echo "HTTPS_PROXY=$HTTPS_PROXY" >> .env
        fi
        
        log_success "代理配置已添加"
    fi
}

# 启动服务
start_services() {
    log_info "启动 MetaTube 服务..."
    
    # 拉取最新镜像
    docker-compose -f docker-compose.cloud.yaml pull || docker-compose pull
    
    # 启动服务
    docker-compose -f docker-compose.cloud.yaml up -d || docker-compose up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务启动..."
    
    for i in {1..30}; do
        if curl -s http://localhost:8080/ > /dev/null; then
            log_success "MetaTube 服务已就绪"
            return
        fi
        sleep 2
    done
    
    log_warning "服务启动可能需要更长时间，请稍后检查"
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 MetaTube 部署完成！"
    echo
    echo "📋 部署信息:"
    echo "  - 服务地址: http://$(curl -s ifconfig.me):8080"
    echo "  - 本地地址: http://localhost:8080"
    echo "  - 项目目录: $PROJECT_DIR"
    echo
    echo "🔑 认证信息:"
    echo "  - API Token: $(grep TOKEN .env | cut -d'=' -f2)"
    echo
    echo "📚 常用命令:"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 停止服务: docker-compose down"
    echo "  - 更新服务: docker-compose pull && docker-compose up -d"
    echo
    echo "🔧 配置文件:"
    echo "  - 环境变量: $PROJECT_DIR/.env"
    echo "  - Docker Compose: $PROJECT_DIR/docker-compose.yaml"
    echo
    echo "📖 使用 Python 客户端测试:"
    echo "  pip install requests"
    echo "  python -c \"import requests; print(requests.get('http://localhost:8080/v1/providers').json())\""
}

# 主函数
main() {
    echo "🚀 MetaTube SDK 云部署脚本"
    echo "================================"
    
    check_root
    detect_os
    install_docker
    install_docker_compose
    configure_firewall
    setup_project
    configure_proxy
    start_services
    wait_for_services
    show_deployment_info
    
    echo
    log_success "部署完成！请访问上述地址开始使用 MetaTube"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
