# MetaTube Python 客户端使用指南

这是一个用于调用 MetaTube SDK Go 服务的 Python 客户端工具，支持搜索影片、获取详细信息等功能。

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 启动本地服务器

如果你有 MetaTube SDK Go 的源码：

```bash
# 启动服务器（会自动构建如果需要）
python metatube_client.py server --port 8080

# 带认证令牌启动
python metatube_client.py server --port 8080 --server-token your-secret-token

# 使用数据库
python metatube_client.py server --port 8080 --dsn "sqlite:///metatube.db"
```

### 2. 搜索影片 "abp-178"

```bash
# 基本搜索（使用所有可用的 providers）
python metatube_client.py search "abp-178"

# 指定使用 FANZA provider
python metatube_client.py search "abp-178" --provider fanza

# 显示失败的 provider 信息
python metatube_client.py search "abp-178" --show-failures

# 连接到远程服务器
python metatube_client.py --url http://your-server:8080 search "abp-178"

# 使用认证令牌
python metatube_client.py --url http://your-server:8080 --token your-token search "abp-178"

# 使用代理服务器启动
python metatube_client.py server --http-proxy http://127.0.0.1:7890 --https-proxy http://127.0.0.1:7890
```

### 3. 获取影片详细信息

```bash
# 获取 FANZA 上的 abp178 详细信息
python metatube_client.py info fanza abp178

# 获取完整信息（非懒加载）
python metatube_client.py info fanza abp178 --no-lazy
```

### 4. 搜索演员

```bash
# 搜索演员
python metatube_client.py actor-search "桃乃木かな"

# 获取演员详细信息
python metatube_client.py actor-info fanza actor123
```

### 5. 查看可用的 Providers

```bash
python metatube_client.py providers
```

## 输出示例

### 搜索结果示例：

```
搜索影片: abp-178
找到 3 个结果:

📺 絶対的美少女、お貸しします。 ACT.78 桃乃木かな
   ID: abp178
   番号: ABP-178
   Provider: FANZA
   评分: 4.2
   发行日期: 2023-01-15
   演员: 桃乃木かな
   链接: https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=abp178/

📺 ABP-178 桃乃木かな
   ID: ABP-178
   番号: ABP-178
   Provider: JavBus
   评分: 0
   发行日期: 2023-01-15
   演员: 桃乃木かな
   链接: https://www.javbus.com/ABP-178

📊 Provider 状态:
   ❌ 失败的 Providers:
      JAVDB: 地区限制
      TokyoHot: 无结果
      SOD: 请求超时
```

### 详细信息示例：

```
获取影片信息: fanza/abp178
🎬 絶対的美少女、お貸しします。 ACT.78 桃乃木かな
   ID: abp178
   番号: ABP-178
   Provider: FANZA
   评分: 4.2
   时长: 120 分钟
   发行日期: 2023-01-15
   制作商: プレステージ
   发行商: ABSOLUTELY PERFECT
   系列: 絶対的美少女、お貸しします。
   导演: 本田教仁
   演员: 桃乃木かな
   类型: 単体作品, 美少女, 中出し, フェラ
   简介: 今回お貸しするのは、透明感溢れる美貌と抜群のスタイルを持つ...
   封面: https://pics.dmm.co.jp/digital/video/abp178/abp178pl.jpg
   链接: https://www.dmm.co.jp/digital/videoa/-/detail/=/cid=abp178/
```

## API 接口说明

客户端支持以下 MetaTube API 接口：

- `GET /v1/movies/search?q=keyword&provider=name&fallback=true` - 搜索影片
- `GET /v1/movies/{provider}/{id}?lazy=true` - 获取影片详细信息
- `GET /v1/actors/search?q=keyword&provider=name` - 搜索演员
- `GET /v1/actors/{provider}/{id}?lazy=true` - 获取演员详细信息
- `GET /v1/providers` - 获取可用的 providers

## 代理设置

### 方法1: 启动时设置代理

```bash
# 启动服务器时设置代理
python metatube_client.py server --http-proxy http://127.0.0.1:7890 --https-proxy http://127.0.0.1:7890

# 快速搜索时使用代理
python quick_start.py --http-proxy http://127.0.0.1:7890
```

### 方法2: 环境变量设置代理

```bash
# 设置环境变量
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890

# 启动服务器（会自动使用环境变量中的代理）
python metatube_client.py server
```

### 方法3: 系统代理

MetaTube SDK Go 会自动使用系统的代理设置，包括：
- Windows: Internet 选项中的代理设置
- macOS/Linux: 环境变量 HTTP_PROXY 和 HTTPS_PROXY

## 错误信息显示

使用 `--show-failures` 参数可以查看哪些 provider 搜索失败以及失败原因：

```bash
python metatube_client.py search "abp-178" --show-failures
```

常见错误类型：
- **地区限制**: Provider 不支持当前地区访问
- **请求超时**: 网络连接超时或服务器响应慢
- **连接失败**: 无法连接到 Provider 服务器
- **无结果**: Provider 中没有找到相关内容
- **访问被拒绝**: 被 Provider 服务器拒绝访问
- **SSL证书错误**: HTTPS 证书验证失败

## 环境变量配置

你也可以通过环境变量配置 MetaTube 服务器：

```bash
# Provider 优先级配置
export MT_MOVIE_PROVIDER_FANZA__PRIORITY=2000
export MT_MOVIE_PROVIDER_JAVBUS__PRIORITY=1500

# 禁用某个 provider
export MT_MOVIE_PROVIDER_JAVDB__PRIORITY=0

# 设置超时时间
export MT_MOVIE_PROVIDER_FANZA__TIMEOUT=30s

# 启动服务器
python metatube_client.py server
```

## 常见问题

### 1. 地区限制问题

如果遇到 FANZA 地区限制，可以：
- 使用其他 provider：`--provider javbus`
- 使用代理服务器
- 依赖系统的回退机制自动选择可用的 provider

### 2. 搜索无结果

- 尝试不同的关键词格式：`ABP-178`, `abp178`, `abp-178`
- 使用不同的 provider
- 检查网络连接

### 3. 服务器启动失败

- 确保已安装 Go 环境
- 检查端口是否被占用
- 查看错误日志

## 高级用法

### 批量搜索脚本

```python
#!/usr/bin/env python3
import subprocess
import sys

movies = ["abp-178", "ssni-001", "mide-123"]

for movie in movies:
    print(f"\n=== 搜索 {movie} ===")
    result = subprocess.run([
        "python", "metatube_client.py", "search", movie
    ], capture_output=True, text=True)
    print(result.stdout)
```

### 作为 Python 模块使用

```python
from metatube_client import MetaTubeClient

# 创建客户端
client = MetaTubeClient("http://localhost:8080")

# 搜索影片
results = client.search_movie("abp-178")
for movie in results:
    print(f"找到: {movie['title']}")

# 获取详细信息
if results:
    info = client.get_movie_info(results[0]['provider'], results[0]['id'])
    print(f"详细信息: {info['title']}")
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！
