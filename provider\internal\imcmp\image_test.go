package imcmp

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSimilar(t *testing.T) {
	for _, item := range []struct {
		imgUrl1, imgUrl2 string
		similar          bool
	}{
		{
			"https://pics.dmm.co.jp/digital/video/1msfh00025/1msfh00025ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1msfh00025/1msfh00025jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1msfh00037/1msfh00037ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1msfh00037/1msfh00037jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00154/1stars00154ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00154/1stars00154jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00249/1stars00249ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00249/1stars00249jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00309/1stars00309ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00309/1stars00309jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00325/1stars00325ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00325/1stars00325jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00330/1stars00330ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00330/1stars00330jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdde00625/1sdde00625ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdde00625/1sdde00625jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdmf00011/1sdmf00011ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdmf00011/1sdmf00011jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdmf00011/1sdmf00011ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdmf00011/1sdmf00011jp-2.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdmf00016/1sdmf00016ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdmf00016/1sdmf00016jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdmf00016/1sdmf00016ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdmf00018/1sdmf00018jp-1.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdmf00018/1sdmf00018ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdmf00018/1sdmf00018jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1sdmf00023/1sdmf00023ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1sdmf00023/1sdmf00023jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200jp-2.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200jp-1.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200jp-2.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200pl.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00200/1stars00200jp-1.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1emoi00035/1emoi00035ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1emoi00035/1emoi00035jp-2.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1emoi00035/1emoi00035ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1emoi00035/1emoi00035jp-3.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/miaa00665/miaa00665ps.jpg",
			"https://pics.dmm.co.jp/digital/video/miaa00665/miaa00665jp-1.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/mide00881/mide00881ps.jpg",
			"https://pics.dmm.co.jp/digital/video/mide00881/mide00881jp-1.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/sivr00155/sivr00155ps.jpg",
			"https://pics.dmm.co.jp/digital/video/sivr00155/sivr00155jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/vrkm00717/vrkm00717ps.jpg",
			"https://pics.dmm.co.jp/digital/video/vrkm00717/vrkm00717jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/sivr00223/sivr00223ps.jpg",
			"https://pics.dmm.co.jp/digital/video/sivr00223/sivr00223jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/vrkm00702/vrkm00702ps.jpg",
			"https://pics.dmm.co.jp/digital/video/vrkm00702/vrkm00702jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/vrkm00703/vrkm00703ps.jpg",
			"https://pics.dmm.co.jp/digital/video/vrkm00703/vrkm00703jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/vrkm00703/vrkm00703ps.jpg",
			"https://pics.dmm.co.jp/digital/video/vrkm00703/vrkm00703jp-2.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/vrkm00703/vrkm00703jp-2.jpg",
			"https://pics.dmm.co.jp/digital/video/vrkm00703/vrkm00703jp-3.jpg",
			false,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1mogi00007/1mogi00007ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1mogi00007/1mogi00007jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stars00311/1stars00311ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stars00311/1stars00311jp-1.jpg",
			true,
		},
		{
			"https://pics.dmm.co.jp/digital/video/1stko00005/1stko00005ps.jpg",
			"https://pics.dmm.co.jp/digital/video/1stko00005/1stko00005jp-1.jpg",
			false,
		},
	} {
		v := Similar(item.imgUrl1, item.imgUrl2, nil)
		assert.True(t, v == item.similar, "%s != %s", item.imgUrl1, item.imgUrl2)
	}
}
